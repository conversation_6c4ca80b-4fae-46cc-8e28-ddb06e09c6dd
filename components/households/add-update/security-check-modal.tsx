import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Button, CircularProgress } from '@mui/material';
import { Close, Security } from '@mui/icons-material';

export type Offender = {
  id?: string;
  name?: string;
  level?: number | string; // 1|2|3
  classification?: number | string;
  details?: any;
};

export type SecurityFinding = {
  address: string;
  lat?: number;
  lon?: number;
  offenders: Offender[];
};

interface SecurityCheckModalProps {
  open: boolean;
  onClose: () => void;
  address: string; // formatted address string
  onSelectFinding: (finding: SecurityFinding) => void; // create tags callback
}

function normalizeAddress(a?: string) {
  return (a || '').trim().toLowerCase().replace(/\s+/g, ' ');
}

function uniqueLevels(offenders: Offender[]): number[] {
  const set = new Set<number>();
  offenders.forEach((o) => {
    const raw = (o.level ?? o.classification) as any;
    const n = Number.parseInt(String(raw), 10);
    if (!Number.isNaN(n)) set.add(n);
  });
  return Array.from(set).sort();
}

async function geocodeWithMapbox(address: string): Promise<{ lat: number; lon: number } | null> {
  try {
    const token = (process as any).env.NEXT_PUBLIC_MAPBOX_TOKEN
      || 'pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg';
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?limit=1&access_token=${token}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Geocoding failed (${res.status})`);
    const data = await res.json();
    const feat = data.features?.[0];
    if (feat?.center?.length === 2) {
      return { lon: feat.center[0], lat: feat.center[1] };
    }
    return null;
  } catch (e) {
    console.error('Geocode error:', e);
    return null;
  }
}

async function offenderSearchByLocation(lat: number, lon: number, normalizedAddr?: string, originalAddr?: string): Promise<SecurityFinding[]> {
  const endpoint = (process as any).env.NEXT_PUBLIC_OFFENDER_API_URL; // e.g., https://your-offender-service/search
  if (!endpoint) {
    // Mock results for UI/testing when endpoint is not configured
    const TARGET = normalizeAddress('1700 Belmont Ave, Seattle, Washington 98122');
    const isTarget = normalizedAddr && (
      normalizedAddr === TARGET
      || (normalizedAddr.includes('1700 belmont ave') && normalizedAddr.includes('seattle') && normalizedAddr.includes('98122'))
    );
    if (isTarget) {
      return [
        {
          address: originalAddr || '1700 Belmont Ave, Seattle, Washington 98122',
          lat,
          lon,
          offenders: [
            { name: 'John Doe', level: 1 },
            { name: 'Jane Smith', level: 3 },
          ],
        },
        {
          address: '1712 Belmont Ave, Seattle, WA 98122',
          lat: lat + 0.0005,
          lon: lon + 0.0005,
          offenders: [
            { name: 'Nearby Person', level: 2 },
          ],
        },
        {
          address: '1600 E Olive St, Seattle, WA 98122',
          lat: lat - 0.0007,
          lon: lon - 0.0002,
          offenders: [
            { level: 1 },
            { level: 2 },
          ],
        },
      ];
    }
    return [];
  }
  try {
    const url = `${endpoint}?lat=${encodeURIComponent(String(lat))}&lon=${encodeURIComponent(String(lon))}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Offender search failed (${res.status})`);
    const data = await res.json();

    // Normalize a couple of common response shapes
    const items: any[] = data?.items || data?.results || [];
    return items.map((it) => ({
      address: it.address || it.formatted || it.location || '',
      lat: it.lat ?? it.latitude,
      lon: it.lon ?? it.lng ?? it.longitude,
      offenders: Array.isArray(it.offenders) ? it.offenders : [],
    }));
  } catch (e) {
    console.error('Offender search error:', e);
    return [];
  }
}

export default function SecurityCheckModal({ open, onClose, address, onSelectFinding }: SecurityCheckModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<SecurityFinding[]>([]);

  const normalizedHouseholdAddr = useMemo(() => normalizeAddress(address), [address]);

  useEffect(() => {
    let cancelled = false;
    async function run() {
      if (!open) return;
      setLoading(true);
      setError(null);
      setResults([]);
      try {
        const coords = await geocodeWithMapbox(address);
        if (!coords) {
          setError('Unable to geocode household address.');
          return;
        }
        const findings = await offenderSearchByLocation(coords.lat, coords.lon, normalizeAddress(address), address);
        if (!cancelled) setResults(findings || []);
      } catch (e: any) {
        if (!cancelled) setError(e?.message || 'Security check failed.');
      } finally {
        if (!cancelled) setLoading(false);
      }
    }
    run();
    return () => { cancelled = true; };
  }, [open, address]);

  const topMatches = useMemo(() => results.filter((r) => normalizeAddress(r.address) === normalizedHouseholdAddr), [results, normalizedHouseholdAddr]);
  const otherMatches = useMemo(() => results.filter((r) => normalizeAddress(r.address) !== normalizedHouseholdAddr), [results, normalizedHouseholdAddr]);

  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-white my-[5%] mx-[10%] xl:mx-[15%] relative rounded-lg overflow-hidden min-w-[520px]">
        <div className="h-[54px] items-center relative justify-center grid grid-flow-col border-b border-[#E0E0E0]">
          <p className="font-[600]">Security Check</p>
          <Close className="absolute right-5 cursor-pointer" onClick={onClose} />
        </div>

        <div className="p-5 pb-6">
          <div className="flex items-center gap-2 mb-3">
            <Security fontSize="small" />
            <p className="m-0 text-sm text-[#646F79]">Searching near: <span className="font-[600]">{address}</span></p>
          </div>

          {loading && (
            <div className="flex items-center gap-2 text-sm text-[#646F79]"><CircularProgress size={18} /> Running location-based offender search…</div>
          )}
          {error && (
            <p className="text-sm text-[#E42B57] m-0">{error}</p>
          )}

          {!loading && !error && (
            <>
              <div className="mb-4">
                <p className="text-[14px] font-[600] m-0">Main Address Lookup</p>
                {topMatches.length === 0 ? (
                  <p className="text-[13px] text-gray-500 m-0">No exact address match found.</p>
                ) : (
                  <div className="grid gap-2 mt-2">
                    {topMatches.map((f, i) => {
                      const levels = uniqueLevels(f.offenders);
                      return (
                        <div key={`top-${i}`} className="border border-[#E0E0E0] rounded-md p-3 grid grid-cols-3 gap-3 items-center">
                          <div className="col-span-2">
                            <p className="m-0 text-[14px] font-[600]">{f.address || 'Unknown address'}</p>
                            <p className="m-0 text-[12px] text-[#646F79]">Offenders: {f.offenders.length} • Levels: {levels.join(', ') || '—'}</p>
                          </div>
                          <div className="flex justify-end">
                            <Button onClick={() => onSelectFinding(f)} className="rounded-md m-0 text-[13px] grey-btn" sx={{ padding: '10px 12px !important' }}>Create Tags</Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              <div className="mb-2">
                <p className="text-[14px] font-[600] m-0">Nearby Matches (2-mile Radius)</p>
                {otherMatches.length === 0 ? (
                  <p className="text-[13px] text-gray-500 m-0">No other nearby matches found.</p>
                ) : (
                  <div className="grid gap-2 mt-2">
                    {otherMatches.map((f, i) => {
                      const levels = uniqueLevels(f.offenders);
                      return (
                        <div key={`other-${i}`} className="border border-[#E0E0E0] rounded-md p-3 grid grid-cols-3 gap-3 items-center">
                          <div className="col-span-2">
                            <p className="m-0 text-[14px] font-[600]">{f.address || 'Unknown address'}</p>
                            <p className="m-0 text-[12px] text-[#646F79]">Offenders: {f.offenders.length} • Levels: {levels.join(', ') || '—'}</p>
                          </div>
                          <div className="flex justify-end">
                            <Button onClick={() => onSelectFinding(f)} className="rounded-md m-0 text-[13px] grey-btn" sx={{ padding: '10px 12px !important' }}>Create Tags</Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}

