import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Button, CircularProgress } from '@mui/material';
import { Close, Security } from '@mui/icons-material';
import { BASE_URL, TOKEN } from 'api/src/lib/api';

// Custom scrollbar styles
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`;

export type Offender = {
  id?: string;
  name?: string;
  level?: number | string; // 1|2|3
  classification?: number | string;
  details?: any;
};

export type SecurityFinding = {
  address: string;
  lat?: number;
  lon?: number;
  offenders: Offender[];
};

interface SecurityCheckModalProps {
  open: boolean;
  onClose: () => void;
  address: string; // formatted address string
  onSelectFinding: (finding: SecurityFinding) => void; // create tags callback
}

function normalizeAddress(a?: string) {
  return (a || '').trim().toLowerCase().replace(/\s+/g, ' ');
}

function uniqueLevels(offenders: Offender[]): number[] {
  const set = new Set<number>();
  offenders.forEach((o) => {
    const raw = (o.level ?? o.classification) as any;
    const n = Number.parseInt(String(raw), 10);
    if (!Number.isNaN(n)) set.add(n);
  });
  return Array.from(set).sort();
}

async function geocodeWithMapbox(address: string): Promise<{ lat: number; lon: number } | null> {
  try {
    const token = (process as any).env.NEXT_PUBLIC_MAPBOX_TOKEN
      || 'pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg';
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?limit=1&access_token=${token}`;
    const res = await fetch(url);
    if (!res.ok) throw new Error(`Geocoding failed (${res.status})`);
    const data = await res.json();
    const feat = data.features?.[0];
    if (feat?.center?.length === 2) {
      return { lon: feat.center[0], lat: feat.center[1] };
    }
    return null;
  } catch (e) {
    console.error('Geocode error:', e);
    return null;
  }
}

// Helper function to map risk level to numeric level (1-3)
function mapRiskLevel(riskLevel?: string): number {
  if (!riskLevel || riskLevel.trim() === '') return 2; // Default to medium
  const level = riskLevel.toLowerCase();
  if (level.includes('high') || level.includes('3')) return 3;
  if (level.includes('low') || level.includes('1')) return 1;
  return 2; // Default to medium
}

// Helper function to parse location coordinates
function parseLocation(location?: string): { lat: number; lon: number } | null {
  if (!location) return null;
  const coords = location.split(',').map(s => parseFloat(s.trim()));
  if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
    return { lat: coords[0], lon: coords[1] };
  }
  return null;
}

// Helper function to format address from API response
function formatAddress(offender: any): string {
  const parts = [];
  if (offender.address) parts.push(offender.address);
  if (offender.city) parts.push(offender.city);
  if (offender.state) parts.push(offender.state);
  if (offender.zip_code) parts.push(offender.zip_code);
  return parts.join(', ') || 'Unknown address';
}

// Helper function to reverse geocode coordinates to address
async function reverseGeocode(lat: number, lon: number): Promise<string | null> {
  try {
    const token = (process as any).env.NEXT_PUBLIC_MAPBOX_TOKEN
      || 'pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg';
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${lon},${lat}.json?limit=1&access_token=${token}`;
    const res = await fetch(url);
    if (!res.ok) return null;
    const data = await res.json();
    return data.features?.[0]?.place_name || null;
  } catch (e) {
    console.error('Reverse geocode error:', e);
    return null;
  }
}

async function offenderSearchByLocation(lat: number, lon: number, _normalizedAddr?: string, originalAddr?: string): Promise<SecurityFinding[]> {
  // Use existing API infrastructure
  const token = TOKEN();

  if (!token) {
    console.warn('User not authenticated. Cannot perform background check.');
    return [];
  }

  try {
    // Make API call to background check service using existing BASE_URL
    const response = await fetch(`${BASE_URL}/api/background-checks/location-search`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        address: originalAddr || '',
        latitude: lat,
        longitude: lon,
        householdId: 'fe3aa001-8465-425b-b6ec-a2de78121db1', // Placeholder for household-level searches
      }),
    });

    if (!response.ok) {
      throw new Error(`Background check API failed (${response.status})`);
    }

    const responseData = await response.json();
    console.log('Background check API response received:', {
      hasExactMatches: responseData?.exactMatches ? responseData.exactMatches.length : 'N/A',
      hasNearbyOffenders: responseData?.nearbyOffenders ? responseData.nearbyOffenders.length : 'N/A',
      totalCount: responseData?.totalCount || 'N/A',
      queueId: responseData?.queueId || 'N/A'
    });

    // Handle new API response format with nested structure
    let allOffenders: any[] = [];

    if (Array.isArray(responseData)) {
      // Backward compatibility: if response is still a direct array
      allOffenders = responseData;
    } else if (responseData && typeof responseData === 'object') {
      // New format: extract offenders from nested structure
      const exactMatches = Array.isArray(responseData.exactMatches) ? responseData.exactMatches : [];
      const nearbyOffenders = Array.isArray(responseData.nearbyOffenders) ? responseData.nearbyOffenders : [];

      // Combine both arrays, with exact matches first
      allOffenders = [...exactMatches, ...nearbyOffenders];

      console.log(`Background check found ${exactMatches.length} exact matches and ${nearbyOffenders.length} nearby offenders`);
    } else {
      console.warn('Unexpected API response format. Expected array or object with exactMatches/nearbyOffenders properties:', {
        responseType: typeof responseData,
        hasExactMatches: 'exactMatches' in (responseData || {}),
        hasNearbyOffenders: 'nearbyOffenders' in (responseData || {}),
        responseKeys: responseData ? Object.keys(responseData) : 'null/undefined'
      });
      return [];
    }

    if (allOffenders.length === 0) {
      console.log('No offenders found in the search area');
      return [];
    }

    // Group offenders by location
    const locationGroups = new Map<string, any[]>();

    for (const offender of allOffenders) {
      const coords = parseLocation(offender.location);
      const address = formatAddress(offender);

      // Use coordinates as key if available, otherwise use address
      const locationKey = coords ? `${coords.lat.toFixed(6)},${coords.lon.toFixed(6)}` : address;

      if (!locationGroups.has(locationKey)) {
        locationGroups.set(locationKey, []);
      }
      locationGroups.get(locationKey)!.push(offender);
    }

    // Convert groups to SecurityFinding objects
    const findings: SecurityFinding[] = [];

    for (const [, groupedOffenders] of locationGroups) {
      const firstOffender = groupedOffenders[0];
      const coords = parseLocation(firstOffender.location);
      let address = formatAddress(firstOffender);

      // If we have coordinates but no proper address, try reverse geocoding
      if (coords && address === 'Unknown address') {
        const reversedAddress = await reverseGeocode(coords.lat, coords.lon);
        if (reversedAddress) {
          address = reversedAddress;
        }
      }

      // Transform offenders to match our model
      const transformedOffenders: Offender[] = groupedOffenders.map(offender => ({
        id: offender.photo_url ? offender.photo_url.split('=').pop() : undefined,
        name: offender.name || 'Unknown',
        level: mapRiskLevel(offender.risk_level),
        classification: mapRiskLevel(offender.risk_level),
        details: {
          aliases: offender.aliases,
          gender: offender.gender,
          age: offender.age,
          eyeColor: offender.eye_color,
          hairColor: offender.hair_color,
          height: offender.height,
          weight: offender.weight,
          marksScarssTattoos: offender.marks_scars_tattoos,
          race: offender.race,
          ethnicity: offender.ethnicity,
          courtRecord: offender.court_record,
          photoUrl: offender.photo_url,
          updateDateTime: offender.update_datetime,
        },
      }));

      findings.push({
        address,
        lat: coords?.lat,
        lon: coords?.lon,
        offenders: transformedOffenders,
      });
    }

    return findings;

  } catch (e) {
    console.error('Offender search error:', e);
    return [];
  }
}

export default function SecurityCheckModal({ open, onClose, address, onSelectFinding }: SecurityCheckModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<SecurityFinding[]>([]);

  const normalizedHouseholdAddr = useMemo(() => normalizeAddress(address), [address]);

  useEffect(() => {
    let cancelled = false;
    async function run() {
      if (!open) return;
      setLoading(true);
      setError(null);
      setResults([]);
      try {
        const coords = await geocodeWithMapbox(address);
        if (!coords) {
          setError('Unable to geocode household address.');
          return;
        }
        const findings = await offenderSearchByLocation(coords.lat, coords.lon, normalizeAddress(address), address);
        if (!cancelled) setResults(findings || []);
      } catch (e: any) {
        if (!cancelled) setError(e?.message || 'Security check failed.');
      } finally {
        if (!cancelled) setLoading(false);
      }
    }
    run();
    return () => { cancelled = true; };
  }, [open, address]);

  const topMatches = useMemo(() => results.filter((r) => normalizeAddress(r.address) === normalizedHouseholdAddr), [results, normalizedHouseholdAddr]);
  const otherMatches = useMemo(() => results.filter((r) => normalizeAddress(r.address) !== normalizedHouseholdAddr), [results, normalizedHouseholdAddr]);

  return (
    <Modal open={open} onClose={onClose}>
      <div className="bg-white my-[5%] mx-[10%] xl:mx-[15%] relative rounded-lg overflow-hidden min-w-[520px] max-h-[90vh] flex flex-col">
        <style>{scrollbarStyles}</style>
        <div className="h-[54px] items-center relative justify-center grid grid-flow-col border-b border-[#E0E0E0] flex-shrink-0">
          <p className="font-[600]">Security Check</p>
          <Close className="absolute right-5 cursor-pointer" onClick={onClose} />
        </div>

        <div className="p-5 pb-6 overflow-y-auto flex-1 custom-scrollbar">
          <div className="flex items-center gap-2 mb-3">
            <Security fontSize="small" />
            <p className="m-0 text-sm text-[#646F79]">Searching near: <span className="font-[600]">{address}</span></p>
          </div>

          {loading && (
            <div className="flex items-center gap-2 text-sm text-[#646F79]"><CircularProgress size={18} /> Running location-based offender search…</div>
          )}
          {error && (
            <p className="text-sm text-[#E42B57] m-0">{error}</p>
          )}

          {!loading && !error && (
            <>
              <div className="mb-4">
                <p className="text-[14px] font-[600] m-0">Main Address Lookup</p>
                {topMatches.length === 0 ? (
                  <p className="text-[13px] text-gray-500 m-0">No exact address match found.</p>
                ) : (
                  <div className="grid gap-2 mt-2 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
                    {topMatches.map((f, i) => {
                      const levels = uniqueLevels(f.offenders);
                      return (
                        <div key={`top-${i}`} className="border border-[#E0E0E0] rounded-md p-3 grid grid-cols-3 gap-3 items-center hover:bg-gray-50 transition-colors">
                          <div className="col-span-2">
                            <p className="m-0 text-[14px] font-[600]">{f.address || 'Unknown address'}</p>
                            <p className="m-0 text-[12px] text-[#646F79]">Offenders: {f.offenders.length} • Levels: {levels.join(', ') || '—'}</p>
                          </div>
                          <div className="flex justify-end">
                            <Button onClick={() => onSelectFinding(f)} className="rounded-md m-0 text-[13px] grey-btn" sx={{ padding: '10px 12px !important' }}>Create Tags</Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              <div className="mb-2">
                <p className="text-[14px] font-[600] m-0">Nearby Matches (2-mile Radius)</p>
                {otherMatches.length === 0 ? (
                  <p className="text-[13px] text-gray-500 m-0">No other nearby matches found.</p>
                ) : (
                  <div className="grid gap-2 mt-2 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
                    {otherMatches.map((f, i) => {
                      const levels = uniqueLevels(f.offenders);
                      return (
                        <div key={`other-${i}`} className="border border-[#E0E0E0] rounded-md p-3 grid grid-cols-3 gap-3 items-center hover:bg-gray-50 transition-colors">
                          <div className="col-span-2">
                            <p className="m-0 text-[14px] font-[600]">{f.address || 'Unknown address'}</p>
                            <p className="m-0 text-[12px] text-[#646F79]">Offenders: {f.offenders.length} • Levels: {levels.join(', ') || '—'}</p>
                          </div>
                          <div className="flex justify-end">
                            <Button onClick={() => onSelectFinding(f)} className="rounded-md m-0 text-[13px] grey-btn" sx={{ padding: '10px 12px !important' }}>Create Tags</Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}

