/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { <PERSON><PERSON>, TextField } from '@mui/material';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import SecurityOutlinedIcon from '@mui/icons-material/SecurityOutlined';
import AddressFormModal from 'components/address-collect/address-form-modal';
import BannerPicker from 'components/banner-picker/banner-picker';
import AddMemberDrawer from 'components/members/add-update/add-members-drawer';
import SelectMember from 'components/members/select-member/select-member';
import CustomTable from 'components/table-components/table';
import SelectTeams from 'components/teams/select-teams/select-teams';
import { useModal } from 'contexts/modal-context/modal-context';
import useHouseholdFormHandler from 'logics/household/use-form-handler';
import useSelectMember from 'logics/member/use-select-member';
import useSelectTeam from 'logics/team/use-select-team';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { HouseHold } from 'types/household-types';
import { capitalizeWords, getLatestImage, isEmptyObject } from 'utils/helper';
import Check from '@mui/icons-material/Check';
import API from 'api/src/lib/api';
import HouseholdTagsDrawer from 'components/households/add-update/household-tags-drawer';
import { HouseholdTag } from 'types/household-types';
import SelectBox from 'components/select-box/select-box';
import SecurityCheckModal, { SecurityFinding } from 'components/households/add-update/security-check-modal';


interface Props {
  household: HouseHold;
  add: boolean;
}

function HouseholdAddUpdate({ household, add }: Props) {
  const { showModal } = useModal();
  const [openAddress, setOpenAddress] = useState(false);
  const {
    formik,
    picture,
    setPicture,
    selectedMembers,
    selectedTeams,
    setSelectedMembers,
    setSelectedTeams,
    setMember,
    member,
  } = useHouseholdFormHandler(add, household.id || '');

  // Household Tags state
  const [openTagSelect, setOpenTagSelect] = useState(false);
  const [householdTags, setHouseholdTags] = useState<HouseholdTag[]>([]);
  const [tagsLoading, setTagsLoading] = useState(false);
  const [tagsError, setTagsError] = useState<string | null>(null);

	  // Security Check state
	  const [openSecurity, setOpenSecurity] = useState(false);
	  const colorByLevel: Record<number, string> = { 1: '#E42B57', 2: '#E97100', 3: '#2370CB' };


  useEffect(() => {
    if (!isEmptyObject(formik?.errors)) {
      if (picture === '') {
        showModal('Missing Fields', 'Please select a image');
      }
      showModal(
        'Missing Fields',
        `
            ${Object.keys(formik?.errors).map(
    (key) => `${(formik as any)?.errors[key]}`,
  )}
          `,
      );
    }
  }, [JSON.stringify(formik?.errors)]);
  const [addressString, setAddressString] = useState('');

  useEffect(() => {
    if (!isEmptyObject(household)) {
      // eslint-disable-next-line no-shadow
      const address: any = household.address.length > 0 && household.address[0];
      // Populate formik values with data from the selected household
      formik.setValues({
        title: household.title,
        street1: address?.street || '',
        street2: address?.street2 || '',
        city: address?.city || '',
        state: address?.state || '',
        zip: address?.zip || '',

        // other values...
      });
      const attachment = getLatestImage(
        household.attachments,
        'householdProfile',
      );
      setPicture(attachment);
      setSelectedMembers(household?.members as any);
      setSelectedTeams(household?.teams as any);
      setAddressString(
        capitalizeWords(
          `${address?.street.trim()}${
            address?.street2 ? ` ${address?.street2.trim()}` : ''
          }, ${address?.city.trim()}, ${address?.state.trim()} ${address?.zip.trim()}`,
          true,
        ),
      );
    }
  }, [household]);

  const {
    MemberSelectorWidget,
    open,
    setOpen,
    openAddMember,
    setOpenAddMember,
  } = useSelectMember(selectedMembers);
  const SelectTeamHook = useSelectTeam(selectedTeams);
  const router = useRouter();

  const handleRemoveTeam = (team: any) => {
    setSelectedTeams((prev) => prev.filter((t) => t.id !== team.id));
  };

  const handleOnDelete = (item: HouseHold) => {
    showModal('Warning', 'Are you sure to delete this household?', async () => {
      await API.HOUSEHOLDS.delete(item.id);
      router.replace('/households');
    });
  };


  // Household Tags: fetch & handlers
  const mapTag = (t: any): HouseholdTag => ({
    id: t.id,
    key: t.key,
    name: t.name,
    color: t.color,
    updatedAt: t.updatedAt || t.updated_at,
    createdAt: t.createdAt || t.created_at,
  });

  const fetchHouseholdTags = async () => {
    if (!household?.id) return;
    setTagsLoading(true);
    setTagsError(null);
    try {
      const resp = await API.HOUSEHOLDS.fetchHouseholdTags(household.id);
      setHouseholdTags(Array.isArray(resp) ? resp.map(mapTag) : []);
    } catch (e) {
      console.error(e);
      setTagsError('Failed to load tags');
    } finally {
      setTagsLoading(false);
    }
  };

  useEffect(() => {
    fetchHouseholdTags();
  }, [household?.id]);

  const handleSaveTags = async (tags: Pick<HouseholdTag, 'name' | 'key' | 'color'>[]) => {
    if (!household?.id) return;
    const existingKeys = new Set(householdTags.map((t) => t.key));
    const toCreate = tags.filter((t) => !existingKeys.has(t.key));
    if (toCreate.length === 0) return;
    setTagsLoading(true);
    setTagsError(null);
    try {
      await API.HOUSEHOLDS.createHouseholdTags(household.id, toCreate);
      await fetchHouseholdTags();
    } catch (e) {
      console.error(e);
      setTagsError('Failed to add tags');
    } finally {
      setTagsLoading(false);
    }
  };

	  // Create tags from selected security finding levels
	  const handleSecuritySelect = async (finding: SecurityFinding) => {
	    try {
	      const levels = new Set<number>();
	      (finding?.offenders || []).forEach((o: any) => {
	        const raw = (o?.level ?? o?.classification) as any;
	        const n = Number.parseInt(String(raw), 10);
	        if (!Number.isNaN(n)) levels.add(n);
	      });
	      const tags = Array.from(levels).map((lvl) => ({
	        name: `level ${lvl}`,
	        key: `level_${lvl}`,
	        color: (colorByLevel as any)[lvl] || '#888888',
	      }));
	      await handleSaveTags(tags);
	      setOpenSecurity(false);
	    } catch (e) {
	      console.error('Failed to create tags from security finding', e);
	    }
	  };


  const removeTag = async (tagId?: string, tagName?: string) => {
    if (!tagName) return;
    if (tagId) {
      try {
        await API.HOUSEHOLDS.deleteHouseholdTag(household.id, tagId);
        setHouseholdTags((prev) => prev.filter((t) => t.name !== tagName));
      } catch (e) {
        console.error('Failed to remove tag:', e);
      }
    } else {
      setHouseholdTags((prev) => prev.filter((t) => t.name !== tagName));
    }
  };

  const removeAllTags = async () => {
    const tagsWithIds = householdTags.filter((t) => typeof t.id === 'string');
    try {
      await Promise.all(tagsWithIds.map((t) => API.HOUSEHOLDS.deleteHouseholdTag(household.id, t.id as string)));
      setHouseholdTags([]);
    } catch (e) {
      console.error('Failed to remove all tags:', e);
    }
  };

  return (
    <>
      <div className="grid grid-cols-3 gap-5 pr-5">
        <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full mt-4 ml-4">
          <p className="text-[21px] p-0 m-0 font-[600] col-span-3">
            <span
              className="font-[300] cursor-pointer"
              onClick={() => {
                router.replace('/households');
              }}
            >
              Households
            </span>
            <span className="font-[300]">{' > '}</span>
            <span>
              {add ? 'Add Household' : `Update Household (${household?.title})`}
            </span>
          </p>
          <div className="flex gap-3">
            <Button
              onClick={() => handleOnDelete(household)}
              component="label"
              className="rounded-md m-0 text-[15px] text-[#E42B57] grey-btn"
              sx={{
                padding: '11px 15px !important',
              }}
            >
              <DeleteOutlinedIcon
                style={{ width: '16px', height: '16px' }}
                className="mr-2"
              />
              Delete Household
            </Button>
            <Button
              onClick={() => setOpenSecurity(true)}
              component="label"
              className="rounded-md m-0 text-[15px] grey-btn"
              sx={{
                padding: '11px 15px !important',
              }}
            >
              <SecurityOutlinedIcon
                style={{ width: '16px', height: '16px' }}
                className="mr-2"
              />
              Security Check
            </Button>
            <Button
              onClick={formik.submitForm}
              variant="contained"
              className="rounded-md h-11  font-[500] text-sm mr-4 !min-w-[100px]"
            >
              <Check style={{ width: '16px', height: '16px' }} className="mr-2" />
              <span className="font-[500]">Save</span>
            </Button>
          </div>
        </div>

        <p className="text-lg p-0 m-0 font-[600] col-span-3 ml-9">
          Household Details
        </p>
        <div className="col-span-3 ml-9">
          <BannerPicker
            picture={picture}
            setPicture={setPicture}
            className="w-[400px]"
          />
        </div>
        <TextField
          value={formik.values.title}
          name="title"
          onChange={formik.handleChange}
          placeholder="Enter Title"
          id="filled-basic"
          label="Title"
          variant="filled"
          className="ml-9 mb-[48px] w-[400px]"
        />
        {/* <TextField
          value={formik.values.street1}
          name="street1"
          onChange={formik.handleChange}
          placeholder="Enter Street 1"
          id="filled-basic"
          label="Street 1"
          variant="filled"
        />
        <TextField
          value={formik.values.street2}
          name="street2"
          onChange={formik.handleChange}
          placeholder="Enter Street 2"
          id="filled-basic"
          label="Street 2"
          variant="filled"
        />
        <TextField
          value={formik.values.city}
          name="city"
          onChange={formik.handleChange}
          placeholder="Enter City"
          id="filled-basic"
          label="City"
          variant="filled"
        />
        <SelectBox
          keyVal="state"
          defaultValue={formik.values.state}
          onChange={formik.handleChange}
          items={constant?.states}
          label="State"
        />
        <HouseholdTagsDrawer
          open={openTagSelect}
          setOpen={setOpenTagSelect}
          handleSave={handleSaveTags}
          existingTags={householdTags}
        />

        <TextField
          value={formik.values.zip}
          name="zip"
          onChange={formik.handleChange}
          placeholder="Enter Zip Code"
          id="filled-basic"
          label="Zip Code"
          variant="filled"
        /> */}
      </div>
      <div className="ml-9 pr-10">
        {formik.values.street1 ? (
          <div className="grid grid-flow-col auto-cols-auto  items-center  place-content-between">
            <p className="text-lg font-[600] m-0 p-0">Address</p>
            <p className="m-0 p-0 text-[14px] mt-2">
              <span
                onClick={() => {
                  setOpenAddress(true);
                }}
                className="text-primary  font-[500] cursor-pointer"
              >
                Edit Address
              </span>
            </p>
          </div>
        ) : (
          <div className="grid mb-[48px]">
            <p className="text-lg font-[600] m-0 p-0">Address</p>

            <p className="text-[#646F79] text-sm mt-2">
              No address has been added for this household.
              <span
                onClick={() => {
                  setOpenAddress(true);
                }}
                className="text-primary  font-[500] cursor-pointer"
              >
                {' + Add Address'}
              </span>
            </p>
          </div>
        )}
      </div>
      {formik.values.street1 && (
        <div className="ml-9 mt-2 mb-[48px] pr-10">
          <CustomTable
            source={[
              {
                address: addressString,
              },
            ]}
            variant="address-table"
            style={{
              border: 'solid 1px #DCDFDF',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        </div>
      )}
      <div className="ml-9 mb-[48px] pr-10">
        <MemberSelectorWidget />
        {selectedMembers.length > 0 && (
          <div className="mt-2">
            <CustomTable
              setSelected={setSelectedMembers}
              source={selectedMembers}
              metadata={{
                householdId: (household as any)?.id as string,
                householdTitle: household.title,
                headId: (household as any).headOfHouse?.id || '',
                setSelected: setSelectedMembers,
                list: [],
              }}
              variant="addsection-member-table"
              style={{
                border: 'solid 1px #DCDFDF',
                borderRadius: '10px',
                borderBottom: 'none',
                borderTop: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderBottom: 'solid 1px #DCDFDF',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
            />
          </div>
        )}
        <AddressFormModal
          open={openAddress}
          setOpen={setOpenAddress}
          formik={formik}
          setAddressString={setAddressString}
        />
        <SelectMember
          selectedMembers={selectedMembers}
          open={open}
          setOpen={setOpen}
          setSelectMembers={setSelectedMembers}
          title="Select Member(s) to Add to Household"
        />
      </div>
      <div className="ml-9 mb-[48px] pr-10">
        <div className="flex justify-between mt-6">
          <p className="text-lg font-[500]">Household Tags</p>
          <div className="flex gap-6">
            <p
              className="text-sm font-[500] text-[15px] text-[#E97100] flex items-center cursor-pointer hover:text-[#F59C4B]"
              onClick={() => setOpenTagSelect(true)}
            >
              Select Tag(s)
            </p>
            {householdTags && householdTags.length > 0 && (
              <p
                className="text-sm font-[500] text-[15px] text-[#E42B57] flex items-center cursor-pointer hover:text-[#F77C99]"
                onClick={removeAllTags}
              >
                Remove All
              </p>
            )}
          </div>
        </div>
        {tagsLoading && (<p className="text-[13px] text-gray-500 italic m-0">Loading tags…</p>)}
        {tagsError && (<p className="text-[13px] text-[#E42B57] italic m-0">{tagsError}</p>)}
        {householdTags && householdTags.length > 0 ? (
          <CustomTable
            variant="household-tags-table"
            source={householdTags}
            style={{ border: 'solid 1px lightgray', borderRadius: '10px', borderBottom: 'none' }}
            headCellStyle={{ padding: '4px 16px', fontSize: '13px', color: '#747A7A' }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
            handleDelete={(tag: any) => removeTag(tag.id, tag.name)}
          />
        ) : (!tagsLoading && !tagsError && (
          <p className="text-[13px] text-gray-500 italic m-0">No tags associated with this household.</p>
        ))}
      </div>

      <div className="ml-9 mb-[48px] pr-10">
        <SelectTeamHook.TeamSelectorWidget />
        {selectedTeams.length > 0 && (
          <div className="mt-2">


            <CustomTable
              source={selectedTeams}
              variant="addsection-team-table"
              style={{
                border: 'solid 1px #DCDFDF',
                borderRadius: '10px',
                borderBottom: 'none',
                borderTop: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderBottom: 'solid 1px #DCDFDF',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
              handleDelete={handleRemoveTeam}
            />
          </div>
        )}
        <SelectTeams
          selectedTeams={selectedTeams}
          setSelectTeams={setSelectedTeams}
          open={SelectTeamHook.open}
          setOpen={SelectTeamHook.setOpen}
        />
        <AddMemberDrawer
          status={[]}
          setMember={setMember}
          member={member}
          setSelectedMembers={setSelectedMembers}
          open={openAddMember}
          setOpen={setOpenAddMember}
        />
      </div>
      <HouseholdTagsDrawer
        open={openTagSelect}
        setOpen={setOpenTagSelect}
        handleSave={handleSaveTags}
        existingTags={householdTags}
      />


	      <SecurityCheckModal
	        open={openSecurity}
	        onClose={() => setOpenSecurity(false)}
	        address={addressString}
	        onSelectFinding={handleSecuritySelect}
	        householdId={household.id}
	      />

    </>
  );
}

export default HouseholdAddUpdate;


