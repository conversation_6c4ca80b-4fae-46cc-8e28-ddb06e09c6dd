# Environment Variables

This document lists the environment variables required for the application.

## Background Check API Configuration

The following environment variables are required for the offender search functionality:

### NEXT_PUBLIC_BACKGROUND_CHECK_API_URL
- **Description**: Base URL for the background check API service
- **Example**: `https://api.backgroundcheck.service.com`
- **Required**: Yes (for live offender search functionality)
- **Note**: If not provided, the offender search will return empty results

### NEXT_PUBLIC_ADMIN_TOKEN
- **Description**: Admin authentication token for background check API calls
- **Example**: `your-admin-token-here`
- **Required**: Yes (for live offender search functionality)
- **Note**: This should be a secure admin token with permissions to access the background check service

## Existing Environment Variables

### NEXT_PUBLIC_MAPBOX_TOKEN
- **Description**: Mapbox API token for geocoding and mapping functionality
- **Example**: `pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg`
- **Required**: Yes (for address geocoding)
- **Note**: Used for converting addresses to coordinates and reverse geocoding

## Setup Instructions

1. Create a `.env.local` file in the project root
2. Add the required environment variables:

```bash
# Background Check API Configuration
NEXT_PUBLIC_BACKGROUND_CHECK_API_URL=https://your-background-check-api.com
NEXT_PUBLIC_ADMIN_TOKEN=your-admin-token-here

# Mapbox Configuration (if not already set)
NEXT_PUBLIC_MAPBOX_TOKEN=your-mapbox-token-here
```

3. Restart the development server for changes to take effect

## Security Notes

- Never commit actual tokens or sensitive values to version control
- Use different tokens for development, staging, and production environments
- Ensure admin tokens have appropriate permissions and are rotated regularly
