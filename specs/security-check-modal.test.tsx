import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SecurityCheckModal from '../components/households/add-update/security-check-modal';

// Mock fetch globally
global.fetch = jest.fn();

// Mock environment variables
const mockEnv = {
  NEXT_PUBLIC_MAPBOX_TOKEN: 'test-mapbox-token',
  NEXT_PUBLIC_BACKGROUND_CHECK_API_URL: 'https://test-api.com',
  NEXT_PUBLIC_ADMIN_TOKEN: 'test-admin-token',
};

// Mock process.env
Object.defineProperty(process, 'env', {
  value: mockEnv,
});

describe('SecurityCheckModal', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    address: '343 MAJORCA AVENUE CORAL GABLES, FL 33134',
    onSelectFinding: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  it('should render the modal when open', () => {
    render(<SecurityCheckModal {...mockProps} />);
    
    expect(screen.getByText('Security Check')).toBeInTheDocument();
    expect(screen.getByText(/Searching near:/)).toBeInTheDocument();
    expect(screen.getByText(mockProps.address)).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(<SecurityCheckModal {...mockProps} open={false} />);
    
    expect(screen.queryByText('Security Check')).not.toBeInTheDocument();
  });

  it('should show loading state initially', () => {
    render(<SecurityCheckModal {...mockProps} />);
    
    expect(screen.getByText(/Running location-based offender search/)).toBeInTheDocument();
  });

  it('should handle successful API response', async () => {
    // Mock Mapbox geocoding response
    const mockGeocodeResponse = {
      features: [{
        center: [-80.2619766, 25.7553134]
      }]
    };

    // Mock background check API response
    const mockOffendersResponse = [
      {
        name: 'Test Offender',
        aliases: 'Test Alias',
        address: '123 Test St',
        city: 'Miami',
        state: 'FL',
        zip_code: '33135',
        location: '25.7657326,-80.2249206',
        risk_level: 'high',
        gender: 'Male',
        age: '35',
        eye_color: 'Brown',
        hair_color: 'Brown',
        height: '6\'0"',
        weight: '180 lbs.',
        marks_scars_tattoos: 'tattoo - left arm',
        race: 'White',
        ethnicity: '',
        court_record: 'Test crime record',
        photo_url: 'https://test.com/photo.jpg',
        update_datetime: '2023-05-17T01:00:22Z'
      }
    ];

    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockGeocodeResponse),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOffendersResponse),
      });

    render(<SecurityCheckModal {...mockProps} />);

    await waitFor(() => {
      expect(screen.queryByText(/Running location-based offender search/)).not.toBeInTheDocument();
    });

    // Verify API calls were made
    expect(fetch).toHaveBeenCalledTimes(2);
    
    // Check Mapbox geocoding call
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('api.mapbox.com/geocoding/v5/mapbox.places/')
    );
    
    // Check background check API call
    expect(fetch).toHaveBeenCalledWith(
      'https://test-api.com/api/background-checks/location-search',
      expect.objectContaining({
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-admin-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: mockProps.address,
          latitude: 25.7553134,
          longitude: -80.2619766,
          memberId: 'household-search',
        }),
      })
    );
  });

  it('should handle missing environment variables gracefully', async () => {
    // Temporarily override environment variables
    const originalEnv = process.env;
    (process as any).env = {
      NEXT_PUBLIC_MAPBOX_TOKEN: 'test-mapbox-token',
      // Missing background check API config
    };

    // Mock Mapbox geocoding response
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        features: [{ center: [-80.2619766, 25.7553134] }]
      }),
    });

    render(<SecurityCheckModal {...mockProps} />);

    await waitFor(() => {
      expect(screen.queryByText(/Running location-based offender search/)).not.toBeInTheDocument();
    });

    // Should show no results when API is not configured
    expect(screen.getByText('No exact address match found.')).toBeInTheDocument();
    expect(screen.getByText('No other nearby matches found.')).toBeInTheDocument();

    // Restore environment
    (process as any).env = originalEnv;
  });

  it('should handle API errors gracefully', async () => {
    // Mock Mapbox geocoding response
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          features: [{ center: [-80.2619766, 25.7553134] }]
        }),
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

    render(<SecurityCheckModal {...mockProps} />);

    await waitFor(() => {
      expect(screen.queryByText(/Running location-based offender search/)).not.toBeInTheDocument();
    });

    // Should show no results when API fails
    expect(screen.getByText('No exact address match found.')).toBeInTheDocument();
    expect(screen.getByText('No other nearby matches found.')).toBeInTheDocument();
  });

  it('should handle geocoding failure', async () => {
    // Mock failed geocoding response
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 400,
    });

    render(<SecurityCheckModal {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByText('Unable to geocode household address.')).toBeInTheDocument();
    });
  });
});
